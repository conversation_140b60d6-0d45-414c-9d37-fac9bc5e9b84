defmodule RepobotWeb.Router do
  use RepobotWeb, :router

  pipeline :browser do
    plug :accepts, ["html"]
    plug :fetch_session
    plug :fetch_live_flash
    plug :put_root_layout, html: {RepobotWeb.Layouts, :root}
    plug :protect_from_forgery
    plug :put_secure_browser_headers
    plug RepobotWeb.Plugs.LoadCurrentUser
    plug RepobotWeb.Plugs.LoadCurrentOrganization
    plug RepobotWeb.Plugs.CheckInstallation
  end

  pipeline :webhook do
    plug :accepts, ["json"]

    plug Plug.Parsers,
      parsers: [:json],
      pass: ["application/json"],
      json_decoder: Jason
  end

  pipeline :require_auth do
    plug RepobotWeb.Plugs.RequireLogin
    plug :put_root_layout, {RepobotWeb.Layouts, :app}
  end

  pipeline :admin do
    plug :browser
    plug RepobotWeb.Plugs.BasicAuth
  end

  scope "/", RepobotWeb do
    pipe_through :browser

    get "/", PageController, :home
    post "/waitlist", PageController, :join_waitlist
    get "/switch-organization/:id", SessionController, :switch_organization
  end

  scope "/", RepobotWeb.Live do
    pipe_through [:browser, :require_auth]

    live "/onboarding", Onboarding.Index
    live "/dashboard", Dashboard.Index
    live "/folders/:id", Folders.Show
    live "/folders/:id/edit", Folders.Edit
    live "/repositories", Repositories.Index
    live "/repositories/:id", Repositories.Show
    live "/repositories/:id/edit", Repositories.Edit
    live "/source-files", SourceFiles.Index
    live "/source-files/new", SourceFiles.New
    live "/source-files/:id", SourceFiles.Show
    live "/source-files/:id/edit", SourceFiles.Edit
    live "/settings", Settings.Index
    live "/sync-map", SyncMap.Index
  end

  scope "/admin", RepobotWeb.Admin do
    pipe_through :admin

    get "/", DashboardController, :index
    resources "/users", UserController

    resources "/organizations", OrganizationController do
      post "/members", OrganizationController, :add_member
      delete "/members/:user_id", OrganizationController, :remove_member
    end

    resources "/repositories", RepositoryController do
      get "/files/:file_id", RepositoryController, :preview_file
      get "/files/:file_id/fetch", RepositoryController, :fetch_file
    end

    resources "/source-files", SourceFileController

    resources "/waitlist", WaitlistController, only: [:index, :delete] do
      post "/generate_code", WaitlistController, :generate_code, as: :generate_code
    end

    get "/events", EventController, :index
    get "/events/:id", EventController, :show
  end

  scope "/auth", RepobotWeb do
    pipe_through :browser

    get "/:provider", AuthController, :request
    get "/:provider/callback", AuthController, :callback
    delete "/logout", AuthController, :delete
  end

  scope "/hooks", RepobotWeb do
    pipe_through :webhook

    post "/", WebhookController, :handle
  end

  # Other scopes may use custom stacks.
  # scope "/api", RepobotWeb do
  #   pipe_through :api
  # end

  # Enable LiveDashboard and Swoosh mailbox preview in development
  if Application.compile_env(:repobot, :dev_routes) do
    # If you want to use the LiveDashboard in production, you should put
    # it behind authentication and allow only admins to access it.
    # If your application does not have an admins-only section yet,
    # you can use Plug.BasicAuth to set up some basic authentication
    # as long as you are also using SSL (which you should anyway).
    import Phoenix.LiveDashboard.Router

    scope "/dev" do
      pipe_through :browser

      live_dashboard "/dashboard", metrics: RepobotWeb.Telemetry
      forward "/mailbox", Plug.Swoosh.MailboxPreview
    end
  end
end
