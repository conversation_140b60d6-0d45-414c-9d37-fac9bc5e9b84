defmodule Repobot.Workers.LoadRepositoryTreeWorker do
  @moduledoc """
  Oban worker for loading repository trees asynchronously.
  
  This worker replaces the Task.start_link calls in RepoTree.load_trees/3
  and provides better reliability, retry logic, and observability.
  """
  
  use Oban.Worker, queue: :files, max_attempts: 3
  
  require Logger
  
  alias Repobot.{Repositories, Repo}
  
  @impl Oban.Worker
  def perform(%Oban.Job{
        args: %{
          "repository_id" => repository_id,
          "user_id" => user_id,
          "receiver_pid" => receiver_pid,
          "component_name" => component_name
        }
      }) do
    # Load the repository and user from the database
    repository = Repo.get!(Repobot.Repository, repository_id)
    user = Repo.get!(Repobot.Accounts.User, user_id)
    
    Logger.info("Loading repository tree for #{repository.full_name}")
    
    case Repositories.refresh_repository_files!(repository.id, user) do
      {:ok, _} ->
        send_to_receiver(receiver_pid, component_name, {:tree_loaded, repository.id})
        :ok
        
      {:error, reason} ->
        Logger.error(
          "Failed to sync repository files for #{repository.full_name}: #{inspect(reason)}"
        )
        
        send_to_receiver(receiver_pid, component_name, {:tree_load_failed, repository.id, reason})
        {:error, reason}
    end
  end
  
  @impl Oban.Worker
  def perform(%Oban.Job{
        args: %{
          "repository_id" => repository_id,
          "user_id" => user_id,
          "receiver_pid" => receiver_pid
        }
      }) do
    # Handle the case without component_name (direct pid receiver)
    repository = Repo.get!(Repobot.Repository, repository_id)
    user = Repo.get!(Repobot.Accounts.User, user_id)
    
    Logger.info("Loading repository tree for #{repository.full_name}")
    
    case Repositories.refresh_repository_files!(repository.id, user) do
      {:ok, _} ->
        send_to_receiver(receiver_pid, nil, {:tree_loaded, repository.id})
        :ok
        
      {:error, reason} ->
        Logger.error(
          "Failed to sync repository files for #{repository.full_name}: #{inspect(reason)}"
        )
        
        send_to_receiver(receiver_pid, nil, {:tree_load_failed, repository.id, reason})
        {:error, reason}
    end
  end
  
  defp send_to_receiver(receiver_pid, nil, message) when is_binary(receiver_pid) do
    # Convert string PID back to actual PID and send directly
    pid = :erlang.binary_to_term(Base.decode64!(receiver_pid))
    send(pid, message)
  end
  
  defp send_to_receiver(receiver_pid, component_name, message) 
       when is_binary(receiver_pid) and is_binary(component_name) do
    # Convert string PID back to actual PID and send with component name
    pid = :erlang.binary_to_term(Base.decode64!(receiver_pid))
    component_atom = String.to_existing_atom(component_name)
    send(pid, {:repo_tree, component_atom, message})
  end
end
