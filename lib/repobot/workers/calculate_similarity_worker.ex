defmodule Repobot.Workers.CalculateSimilarityWorker do
  @moduledoc """
  Oban worker for calculating file similarity asynchronously.
  
  This worker replaces the Task.start_link calls in Files.calculate_common_files_similarity/3
  and provides better reliability, retry logic, and observability.
  """
  
  use Oban.Worker, queue: :files, max_attempts: 3
  
  require Logger
  
  alias Repobot.{Files, Repo}
  
  @impl Oban.Worker
  def perform(%Oban.Job{
        args: %{
          "common_files" => common_files,
          "repository_ids" => repository_ids,
          "receiver_pid" => receiver_pid,
          "component_name" => component_name
        }
      }) do
    # Load repositories with files preloaded
    repositories = 
      repository_ids
      |> Enum.map(&Repo.get!(Repobot.Repository, &1))
      |> Enum.map(&Repo.preload(&1, :files, force: true))
    
    total_files = length(common_files)
    
    if total_files > 0 do
      try do
        result =
          common_files
          |> Stream.with_index(1)
          |> Enum.reduce_while({:ok, []}, fn {file, index}, {:ok, acc} ->
            # Calculate and send progress
            progress = floor(index * 100 / total_files)
            send_to_receiver(receiver_pid, component_name, {:similarity_progress, progress})
            
            # Get file content from each repository that has this file
            contents_with_repo_info =
              repositories
              |> Enum.map(fn repo ->
                case Enum.find(repo.files, &(&1.path == file["path"] && &1.type == "file")) do
                  nil -> nil
                  repo_file -> {repo_file.content, repo.id}
                end
              end)
              |> Enum.reject(&is_nil/1)
            
            contents =
              Enum.map(contents_with_repo_info, &elem(&1, 0))
              |> Enum.reject(&is_nil/1)
            
            # Get the actual content from the first available source
            actual_content =
              Enum.find_value(contents_with_repo_info, fn {content, _repo_id} -> content end)
            
            # Skip files where all content is nil
            if Enum.empty?(contents) do
              # All repositories have nil content for this file, skip it
              {:cont, {:ok, acc}}
            else
              # Calculate similarity only if we have content from at least 2 repos
              similarity =
                if length(contents) >= 2 do
                  Files.calculate_similarity(contents)
                else
                  # If only 1 repo has the file, similarity is 100
                  100
                end
              
              # Start with original map and add similarity and content
              file_with_similarity_and_content =
                file
                |> Map.put("similarity", similarity)
                |> Map.put("content", actual_content)
                |> Map.put("source_repo_id", elem(List.first(contents_with_repo_info), 1))
              
              {:cont, {:ok, [file_with_similarity_and_content | acc]}}
            end
          end)
        
        case result do
          {:ok, files} ->
            files =
              files
              |> Enum.reverse()
              |> Enum.sort_by(&{-&1["similarity"], -&1["count"], &1["path"]})
            
            send_to_receiver(receiver_pid, component_name, {:similarity_complete, files})
            :ok
            
          {:error, reason} ->
            send_to_receiver(receiver_pid, component_name, {:similarity_error, reason})
            {:error, reason}
        end
      rescue
        e ->
          Logger.error(
            "Error calculating similarity: #{Exception.format(:error, e, __STACKTRACE__)}"
          )
          
          send_to_receiver(receiver_pid, component_name, {:similarity_error, Exception.message(e)})
          {:error, Exception.message(e)}
      end
    else
      send_to_receiver(receiver_pid, component_name, {:similarity_complete, []})
      :ok
    end
  end
  
  @impl Oban.Worker
  def perform(%Oban.Job{
        args: %{
          "common_files" => common_files,
          "repository_ids" => repository_ids,
          "receiver_pid" => receiver_pid
        }
      }) do
    # Handle the case without component_name (direct pid receiver)
    repositories = 
      repository_ids
      |> Enum.map(&Repo.get!(Repobot.Repository, &1))
      |> Enum.map(&Repo.preload(&1, :files, force: true))
    
    total_files = length(common_files)
    
    if total_files > 0 do
      try do
        result =
          common_files
          |> Stream.with_index(1)
          |> Enum.reduce_while({:ok, []}, fn {file, index}, {:ok, acc} ->
            # Calculate and send progress
            progress = floor(index * 100 / total_files)
            send_to_receiver(receiver_pid, nil, {:similarity_progress, progress})
            
            # Get file content from each repository that has this file
            contents_with_repo_info =
              repositories
              |> Enum.map(fn repo ->
                case Enum.find(repo.files, &(&1.path == file["path"] && &1.type == "file")) do
                  nil -> nil
                  repo_file -> {repo_file.content, repo.id}
                end
              end)
              |> Enum.reject(&is_nil/1)
            
            contents =
              Enum.map(contents_with_repo_info, &elem(&1, 0))
              |> Enum.reject(&is_nil/1)
            
            # Get the actual content from the first available source
            actual_content =
              Enum.find_value(contents_with_repo_info, fn {content, _repo_id} -> content end)
            
            # Skip files where all content is nil
            if Enum.empty?(contents) do
              {:cont, {:ok, acc}}
            else
              # Calculate similarity
              similarity =
                if length(contents) >= 2 do
                  Files.calculate_similarity(contents)
                else
                  100
                end
              
              file_with_similarity_and_content =
                file
                |> Map.put("similarity", similarity)
                |> Map.put("content", actual_content)
                |> Map.put("source_repo_id", elem(List.first(contents_with_repo_info), 1))
              
              {:cont, {:ok, [file_with_similarity_and_content | acc]}}
            end
          end)
        
        case result do
          {:ok, files} ->
            files =
              files
              |> Enum.reverse()
              |> Enum.sort_by(&{-&1["similarity"], -&1["count"], &1["path"]})
            
            send_to_receiver(receiver_pid, nil, {:similarity_complete, files})
            :ok
            
          {:error, reason} ->
            send_to_receiver(receiver_pid, nil, {:similarity_error, reason})
            {:error, reason}
        end
      rescue
        e ->
          Logger.error(
            "Error calculating similarity: #{Exception.format(:error, e, __STACKTRACE__)}"
          )
          
          send_to_receiver(receiver_pid, nil, {:similarity_error, Exception.message(e)})
          {:error, Exception.message(e)}
      end
    else
      send_to_receiver(receiver_pid, nil, {:similarity_complete, []})
      :ok
    end
  end
  
  defp send_to_receiver(receiver_pid, nil, message) when is_binary(receiver_pid) do
    # Convert string PID back to actual PID and send directly
    pid = :erlang.binary_to_term(Base.decode64!(receiver_pid))
    send(pid, message)
  end
  
  defp send_to_receiver(receiver_pid, component_name, message) 
       when is_binary(receiver_pid) and is_binary(component_name) do
    # Convert string PID back to actual PID and send with component name
    pid = :erlang.binary_to_term(Base.decode64!(receiver_pid))
    component_atom = String.to_existing_atom(component_name)
    send(pid, {:files, component_atom, message})
  end
end
