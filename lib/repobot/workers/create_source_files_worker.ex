defmodule Repobot.Workers.CreateSourceFilesWorker do
  @moduledoc """
  Oban worker for creating source files during onboarding finalization.
  
  This worker handles the source file creation process that was previously
  done synchronously in the Summary step.
  """
  
  use Oban.Worker, queue: :onboarding, max_attempts: 3
  
  require Logger
  
  alias Repobot.{Repo, SourceFiles, Folders}
  
  @impl Oban.Worker
  def perform(%Oban.Job{
        args: %{
          "selected_files" => selected_files,
          "template_repo_id" => template_repo_id,
          "user_id" => user_id,
          "target_folder_ids" => target_folder_ids,
          "receiver_pid" => receiver_pid
        }
      }) do
    # Load the template repository, user, and target folders
    template_repo = Repo.get!(Repobot.Repository, template_repo_id)
    user = Repo.get!(Repobot.Accounts.User, user_id)
    target_folders = Enum.map(target_folder_ids, &Repo.get!(Repobot.Folder, &1))
    
    Logger.info("Creating source files for template repository #{template_repo.full_name}")
    
    case create_source_files(selected_files, template_repo, user, target_folders) do
      {:ok, newly_created_source_files} ->
        # Send success message to the receiver
        send_to_receiver(receiver_pid, {:create_source_files, newly_created_source_files, template_repo})
        :ok
        
      {:error, reason} ->
        Logger.error("Failed to create source files: #{inspect(reason)}")
        send_to_receiver(receiver_pid, {:create_source_files_error, reason})
        {:error, reason}
    end
  end
  
  defp create_source_files(selected_files, template_repo, current_user, target_folders) do
    Enum.reduce_while(selected_files, {:ok, []}, fn file, {:ok, acc} ->
      Logger.debug(
        "[create_source_files] Processing file: #{file["path"]}, Input content size: #{byte_size(file["content"] || "")}"
      )
      
      # Get all repositories from target folders to associate with the source file
      repositories_to_associate =
        target_folders
        |> Enum.flat_map(fn folder ->
          Repo.preload(folder, :repositories).repositories
        end)
        |> Enum.uniq_by(& &1.id)
      
      # Use the same approach as the import_file functionality in Repositories.Show
      # but import FROM the template repository (not from target repositories)
      attrs = %{
        name: Path.basename(file["path"]),
        content: file["content"],
        target_path: file["path"],
        organization_id: current_user.default_organization_id,
        # Use template repository as source instead of target repository
        source_repository_id: template_repo.id,
        read_only: template_repo.template,
        user_id: current_user.id
      }
      
      Logger.debug(
        "[create_source_files] Attrs passed to SourceFiles.import_file: #{inspect(Map.put(attrs, :content, "[CONTENT REDACTED]"))}"
      )
      
      case SourceFiles.import_file(attrs, repositories_to_associate, target_folders) do
        {:ok, source_file} ->
          Logger.debug("[create_source_files] Successfully created source file: #{source_file.id}")
          {:cont, {:ok, [source_file | acc]}}
          
        {:error, reason} ->
          Logger.error("[create_source_files] Failed to create source file for #{file["path"]}: #{inspect(reason)}")
          {:halt, {:error, reason}}
      end
    end)
  end
  
  defp send_to_receiver(receiver_pid, message) when is_binary(receiver_pid) do
    # Convert string PID back to actual PID and send message
    pid = :erlang.binary_to_term(Base.decode64!(receiver_pid))
    send(pid, message)
  end
end
