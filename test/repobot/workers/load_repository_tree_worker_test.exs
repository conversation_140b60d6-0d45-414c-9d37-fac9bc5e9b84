defmodule Repobot.Workers.LoadRepositoryTreeWorkerTest do
  use Repobot.DataCase, async: true
  use Oban.Testing, repo: Repobot.Repo

  alias Repobot.Workers.LoadRepositoryTreeWorker
  alias Repobot.{Repositories, Repo}

  import Repobot.Test.Fixtures

  setup do
    user = user_fixture()
    organization = organization_fixture(user)
    repository = repository_fixture(organization)
    
    %{user: user, organization: organization, repository: repository}
  end

  describe "perform/1" do
    test "successfully loads repository tree with component name", %{user: user, repository: repository} do
      # Mock the refresh_repository_files! function
      expect(Repobot.Test.GitHubMock, :refresh_repository_files!, fn _, _ ->
        {:ok, repository}
      end)

      # Create job args with component name
      job_args = %{
        "repository_id" => repository.id,
        "user_id" => user.id,
        "receiver_pid" => Base.encode64(:erlang.term_to_binary(self())),
        "component_name" => "template_files"
      }

      job = LoadRepositoryTreeWorker.new(%{args: job_args})

      assert :ok = perform_job(LoadRepositoryTreeWorker, job_args)

      # Should receive the success message
      assert_receive {:repo_tree, :template_files, {:tree_loaded, repository_id}}
      assert repository_id == repository.id
    end

    test "successfully loads repository tree without component name", %{user: user, repository: repository} do
      # Mock the refresh_repository_files! function
      expect(Repobot.Test.GitHubMock, :refresh_repository_files!, fn _, _ ->
        {:ok, repository}
      end)

      # Create job args without component name
      job_args = %{
        "repository_id" => repository.id,
        "user_id" => user.id,
        "receiver_pid" => Base.encode64(:erlang.term_to_binary(self()))
      }

      job = LoadRepositoryTreeWorker.new(%{args: job_args})

      assert :ok = perform_job(LoadRepositoryTreeWorker, job_args)

      # Should receive the success message directly
      assert_receive {:tree_loaded, repository_id}
      assert repository_id == repository.id
    end

    test "handles repository tree loading failure with component name", %{user: user, repository: repository} do
      error_reason = "GitHub API error"
      
      # Mock the refresh_repository_files! function to return error
      expect(Repobot.Test.GitHubMock, :refresh_repository_files!, fn _, _ ->
        {:error, error_reason}
      end)

      # Create job args with component name
      job_args = %{
        "repository_id" => repository.id,
        "user_id" => user.id,
        "receiver_pid" => Base.encode64(:erlang.term_to_binary(self())),
        "component_name" => "template_files"
      }

      job = LoadRepositoryTreeWorker.new(%{args: job_args})

      assert {:error, ^error_reason} = perform_job(LoadRepositoryTreeWorker, job_args)

      # Should receive the error message
      assert_receive {:repo_tree, :template_files, {:tree_load_failed, repository_id, ^error_reason}}
      assert repository_id == repository.id
    end

    test "handles repository tree loading failure without component name", %{user: user, repository: repository} do
      error_reason = "GitHub API error"
      
      # Mock the refresh_repository_files! function to return error
      expect(Repobot.Test.GitHubMock, :refresh_repository_files!, fn _, _ ->
        {:error, error_reason}
      end)

      # Create job args without component name
      job_args = %{
        "repository_id" => repository.id,
        "user_id" => user.id,
        "receiver_pid" => Base.encode64(:erlang.term_to_binary(self()))
      }

      job = LoadRepositoryTreeWorker.new(%{args: job_args})

      assert {:error, ^error_reason} = perform_job(LoadRepositoryTreeWorker, job_args)

      # Should receive the error message directly
      assert_receive {:tree_load_failed, repository_id, ^error_reason}
      assert repository_id == repository.id
    end

    test "handles missing repository", %{user: user} do
      non_existent_id = -1

      job_args = %{
        "repository_id" => non_existent_id,
        "user_id" => user.id,
        "receiver_pid" => Base.encode64(:erlang.term_to_binary(self()))
      }

      job = LoadRepositoryTreeWorker.new(%{args: job_args})

      assert_raise Ecto.NoResultsError, fn ->
        perform_job(LoadRepositoryTreeWorker, job_args)
      end
    end

    test "handles missing user", %{repository: repository} do
      non_existent_id = -1

      job_args = %{
        "repository_id" => repository.id,
        "user_id" => non_existent_id,
        "receiver_pid" => Base.encode64(:erlang.term_to_binary(self()))
      }

      job = LoadRepositoryTreeWorker.new(%{args: job_args})

      assert_raise Ecto.NoResultsError, fn ->
        perform_job(LoadRepositoryTreeWorker, job_args)
      end
    end
  end
end
