import Config

# Configure your database
#
# The MIX_TEST_PARTITION environment variable can be used
# to provide built-in test partitioning in CI environment.
# Run `mix help test` for more information.
config :repobot, Repobot.Repo,
  username: "postgres",
  password: "postgres",
  hostname: System.get_env("DATABASE_HOST") || "localhost",
  database: "repobot_test#{System.get_env("MIX_TEST_PARTITION")}",
  pool: Ecto.Adapters.SQL.Sandbox,
  pool_size: System.schedulers_online() * 2

# We don't run a server during test. If one is required,
# you can enable the server option below.
config :repobot, RepobotWeb.Endpoint,
  http: [ip: {127, 0, 0, 1}, port: 4002],
  secret_key_base: "izZRA/4kxKQ3vA5tFXsN/Ga6S0zMQVi2V1moG2hBX8xfckkIuz4bEMPLNgaP24pG",
  server: false

# In test we don't send emails.
config :repobot, Repobot.Mailer, adapter: Swoosh.Adapters.Test
config :repobot, :url_host, "http://localhost:4000"

# Disable swoosh api client as it is only required for production adapters.
config :swoosh, :api_client, false

config :logger, level: :debug

# Configure default handler to log to file instead of stdout
config :logger, :default_handler,
  config: %{
    file: ~c"log/test.log",
    filesync_repeat_interval: 5000,
    file_check: 5000,
    max_no_bytes: 10_000_000,
    max_no_files: 5
  },
  formatter:
    {LoggerJSON.Formatters.Basic,
     metadata: [
       :file,
       :line,
       :function,
       :request_id,
       :user_id,
       :organization_id,
       :event,
       :reason,
       :user_login,
       :result
     ],
     dev_mode: true}

# config :logger, level: :debug

# Initialize plugs at runtime for faster test compilation
config :phoenix, :plug_init_mode, :runtime

config :phoenix_live_view,
  # Enable helpful, but potentially expensive runtime checks
  enable_expensive_runtime_checks: true

config :repobot, :github_api, Repobot.Test.GitHubMock
config :repobot, :ai_backend, Repobot.Test.AIMock
config :repobot, :sync_backend, Repobot.Test.SyncMock
config :repobot, :signature_verifier, Repobot.Test.SignatureVerifierMock

config :repobot,
  github_oauth_req_options: [
    plug: {Req.Test, Repobot.Accounts}
  ]

config :repobot, :github_app,
  webhook_secret: "test_secret",
  app_id: "test_app_id",
  private_key: "test_private_key"

# Configure Oban for testing
config :repobot, Oban, testing: :inline
